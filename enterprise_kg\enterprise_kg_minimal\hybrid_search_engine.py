"""
Hybrid Search Engine for Enterprise KG Minimal

This module implements hybrid search combining semantic search (Pinecone-compatible)
with Neo4j knowledge graph queries, following the HYBRID_SEARCH_IMPLEMENTATION_GUIDE.md.

Features:
- Template-based query approach for common patterns
- Entity discovery approach for complex queries
- Pinecone-compatible semantic search integration
- Neo4j knowledge graph relationship queries
- Result combination and ranking
"""

import logging
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import hashlib
import re

# Import our existing components
from storage.neo4j_client import Neo4jClient
from constants.entities import EntityType
from constants.relationships import RelationshipType


logger = logging.getLogger(__name__)


class SearchMethod(Enum):
    """Available search methods."""
    SEMANTIC_ONLY = "semantic_only"
    GRAPH_ONLY = "graph_only"
    TEMPLATE_HYBRID = "template_hybrid"
    DISCOVERY_HYBRID = "discovery_hybrid"
    AUTO_HYBRID = "auto_hybrid"


class QueryIntent(Enum):
    """Detected query intents for template-based search."""
    WHO_WORKS_ON = "who_works_on"
    WHAT_SYSTEMS = "what_systems"
    WHO_MANAGES = "who_manages"
    WHAT_PROJECTS = "what_projects"
    HOW_CONNECTED = "how_connected"
    GENERAL_SEARCH = "general_search"


@dataclass
class SearchResult:
    """Represents a search result with metadata."""
    content: str
    source: str
    score: float
    result_type: str  # "semantic", "graph", "hybrid"
    metadata: Dict[str, Any]


@dataclass
class HybridSearchResponse:
    """Complete hybrid search response."""
    query: str
    method: str
    answer: str
    semantic_search: Dict[str, Any]
    knowledge_graph: Dict[str, Any]
    source_files: List[str]
    confidence: float
    processing_info: Dict[str, Any]


class SemanticSearchClient:
    """
    Mock semantic search client that can be replaced with actual Pinecone integration.
    This provides the interface for semantic search functionality.
    """
    
    def __init__(self, index_name: Optional[str] = None, api_key: Optional[str] = None):
        """Initialize semantic search client."""
        self.index_name = index_name
        self.api_key = api_key
        self.mock_mode = True  # Set to False when using real Pinecone
        
        # Mock data for demonstration
        self.mock_chunks = [
            {
                "id": "chunk_123",
                "score": 0.89,
                "metadata": {
                    "file_id": "project_alpha_status.md",
                    "org_id": "tech_corp_001",
                    "chunk_text": "John Doe is the project manager for Project Alpha. Sarah Smith leads the development team working on the new CRM integration."
                }
            },
            {
                "id": "chunk_456",
                "score": 0.82,
                "metadata": {
                    "file_id": "team_assignments.md",
                    "org_id": "tech_corp_001",
                    "chunk_text": "Project Alpha team includes Mike Johnson (UI Designer) and Lisa Chen (Data Analyst). They collaborate with the Design Department."
                }
            },
            {
                "id": "chunk_789",
                "score": 0.75,
                "metadata": {
                    "file_id": "system_architecture.md",
                    "org_id": "tech_corp_001",
                    "chunk_text": "The CRM System integrates with Analytics Dashboard and Mobile App. Mike Johnson's team handles the UI components."
                }
            }
        ]
    
    async def search(self, query: str, top_k: int = 10, filters: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Perform semantic search.
        
        Args:
            query: Search query
            top_k: Number of results to return
            filters: Optional filters (e.g., org_id)
            
        Returns:
            List of search results with scores and metadata
        """
        if self.mock_mode:
            # Return mock results for demonstration
            logger.info(f"Mock semantic search for: {query}")
            return self.mock_chunks[:top_k]
        else:
            # TODO: Implement actual Pinecone integration
            # query_embedding = create_embedding(query)
            # results = pinecone_index.query(
            #     vector=query_embedding,
            #     top_k=top_k,
            #     filter=filters,
            #     include_metadata=True
            # )
            # return results.matches
            raise NotImplementedError("Real Pinecone integration not implemented yet")


class QueryIntentDetector:
    """Detects user intent from query patterns for template-based search."""
    
    def __init__(self):
        """Initialize intent detection patterns."""
        self.intent_patterns = {
            QueryIntent.WHO_WORKS_ON: [
                r"who.*work.*on",
                r"who.*involved.*in",
                r"who.*assigned.*to",
                r"team.*members.*for"
            ],
            QueryIntent.WHAT_SYSTEMS: [
                r"what.*system",
                r"which.*system",
                r"what.*tool",
                r"what.*platform"
            ],
            QueryIntent.WHO_MANAGES: [
                r"who.*manage",
                r"who.*lead",
                r"who.*responsible.*for",
                r"manager.*of"
            ],
            QueryIntent.WHAT_PROJECTS: [
                r"what.*project",
                r"which.*project",
                r"project.*involve",
                r"working.*on.*project"
            ],
            QueryIntent.HOW_CONNECTED: [
                r"how.*connect",
                r"relationship.*between",
                r"how.*relate",
                r"connection.*between"
            ]
        }
        
        # Compile patterns for efficiency
        self.compiled_patterns = {
            intent: [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
            for intent, patterns in self.intent_patterns.items()
        }
    
    def detect_intent(self, query: str) -> QueryIntent:
        """
        Detect the intent of a query.
        
        Args:
            query: User query
            
        Returns:
            Detected query intent
        """
        query_lower = query.lower()
        
        for intent, patterns in self.compiled_patterns.items():
            for pattern in patterns:
                if pattern.search(query_lower):
                    logger.info(f"Detected intent: {intent.value} for query: {query}")
                    return intent
        
        logger.info(f"No specific intent detected, using general search for: {query}")
        return QueryIntent.GENERAL_SEARCH
    
    def extract_entities(self, query: str) -> List[str]:
        """
        Extract entity names from query.

        Args:
            query: User query

        Returns:
            List of extracted entity names
        """
        # Simple entity extraction using capitalized words and quoted strings
        entities = []

        # Question words and common words to filter out
        stop_words = {
            'who', 'what', 'where', 'when', 'why', 'how', 'which', 'whose',
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
            'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do',
            'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
            'must', 'can', 'works', 'work', 'working', 'involved', 'manages',
            'manage', 'managing', 'uses', 'use', 'using', 'connects', 'connect'
        }

        # Extract quoted strings
        quoted_entities = re.findall(r'"([^"]*)"', query)
        entities.extend(quoted_entities)

        # Extract capitalized words (potential proper nouns)
        words = query.split()
        for word in words:
            # Remove punctuation and check if capitalized
            clean_word = re.sub(r'[^\w\s]', '', word)
            if (clean_word and
                clean_word[0].isupper() and
                len(clean_word) > 2 and
                clean_word.lower() not in stop_words):
                if clean_word not in entities:
                    entities.append(clean_word)

        logger.info(f"Extracted entities: {entities} from query: {query}")
        return entities


class TemplateQueryBuilder:
    """Builds Cypher queries from templates based on detected intent."""
    
    def __init__(self):
        """Initialize query templates."""
        self.templates = {
            QueryIntent.WHO_WORKS_ON: """
                MATCH (person)-[r:INVOLVED_IN|LEADS|OWNS]->(target)
                WHERE target.name CONTAINS $entity_name
                AND (person:Person OR person.entity_type = 'Person')
                AND (target:Project OR target.entity_type = 'Project')
                RETURN person.name as person, person.entity_type as person_type,
                       type(r) as relationship, target.name as target, target.entity_type as target_type
            """,

            QueryIntent.WHAT_SYSTEMS: """
                MATCH (source)-[r1:INVOLVED_IN|PART_OF]->(intermediate)
                MATCH (intermediate)-[r2:INTEGRATES_WITH|USES]->(system)
                WHERE source.name CONTAINS $entity_name
                AND (system:System OR system.entity_type = 'System')
                RETURN source.name, intermediate.name, system.name, type(r1), type(r2)

                UNION

                MATCH (source)-[r:INTEGRATES_WITH|USES|PART_OF]->(system)
                WHERE source.name CONTAINS $entity_name
                AND (system:System OR system.entity_type = 'System')
                RETURN source.name, null as intermediate, system.name, type(r), null as r2
            """,

            QueryIntent.WHO_MANAGES: """
                MATCH (manager)-[r:LEADS|OWNS]->(target)
                WHERE target.name CONTAINS $entity_name
                AND (manager:Person OR manager.entity_type = 'Person')
                RETURN manager.name as manager, manager.entity_type as manager_type,
                       type(r) as relationship, target.name as target, target.entity_type as target_type
            """,

            QueryIntent.WHAT_PROJECTS: """
                MATCH (person)-[r:INVOLVED_IN|LEADS|OWNS]->(project)
                WHERE person.name CONTAINS $entity_name
                AND (project:Project OR project.entity_type = 'Project')
                RETURN person.name as person, type(r) as relationship,
                       project.name as project, project.entity_type as project_type
            """,

            QueryIntent.HOW_CONNECTED: """
                MATCH path = (entity1)-[*1..3]-(entity2)
                WHERE entity1.name CONTAINS $entity1_name
                AND entity2.name CONTAINS $entity2_name
                RETURN entity1.name, entity2.name,
                       [r in relationships(path) | type(r)] as relationship_path,
                       length(path) as path_length
                ORDER BY path_length
                LIMIT 10
            """
        }
    
    def build_query(self, intent: QueryIntent, entities: List[str]) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query based on intent and entities.
        
        Args:
            intent: Detected query intent
            entities: Extracted entities from query
            
        Returns:
            Tuple of (cypher_query, parameters)
        """
        if intent not in self.templates:
            # Fallback to general search
            return self._build_general_query(entities)
        
        template = self.templates[intent]
        parameters = self._build_parameters(intent, entities)
        
        logger.info(f"Built query for intent {intent.value} with entities {entities}")
        return template, parameters

    def _build_parameters(self, intent: QueryIntent, entities: List[str]) -> Dict[str, Any]:
        """Build parameters for Cypher query based on intent."""
        params = {}

        if entities:
            params["entity_name"] = entities[0]  # Use first entity as primary

            if len(entities) > 1:
                params["entity1_name"] = entities[0]
                params["entity2_name"] = entities[1]

        # Add type filters based on intent
        if intent in [QueryIntent.WHO_WORKS_ON, QueryIntent.WHO_MANAGES]:
            params["person_types"] = ["Person", "Employee", "Manager", "Developer"]
            params["target_types"] = ["Project", "Initiative", "Program", "Task"]

        elif intent == QueryIntent.WHAT_SYSTEMS:
            params["system_types"] = ["System", "Application", "Platform", "Tool", "Database"]

        elif intent == QueryIntent.WHAT_PROJECTS:
            params["project_types"] = ["Project", "Initiative", "Program", "Task"]

        return params

    def _build_general_query(self, entities: List[str]) -> Tuple[str, Dict[str, Any]]:
        """Build a general search query when no specific intent is detected."""
        if not entities:
            query = """
                MATCH (e)-[r]->(t)
                RETURN e.name, type(r), t.name, e.entity_type, t.entity_type
                LIMIT 20
            """
            return query, {}

        query = """
            MATCH (e)
            WHERE e.name CONTAINS $entity_name
            OPTIONAL MATCH (e)-[r]->(t)
            RETURN e.name, e.entity_type, type(r), t.name, t.entity_type
            LIMIT 20
        """
        return query, {"entity_name": entities[0]}


class HybridSearchEngine:
    """
    Main hybrid search engine that combines semantic and graph search.

    This follows the implementation guide and provides both template-based
    and entity discovery approaches for hybrid search.
    """

    def __init__(
        self,
        neo4j_client: Neo4jClient,
        semantic_client: Optional[SemanticSearchClient] = None,
        default_method: SearchMethod = SearchMethod.AUTO_HYBRID
    ):
        """
        Initialize the hybrid search engine.

        Args:
            neo4j_client: Neo4j client for graph queries
            semantic_client: Semantic search client (Pinecone-compatible)
            default_method: Default search method to use
        """
        self.neo4j_client = neo4j_client
        self.semantic_client = semantic_client or SemanticSearchClient()
        self.default_method = default_method

        # Initialize components
        self.intent_detector = QueryIntentDetector()
        self.query_builder = TemplateQueryBuilder()

    async def search(
        self,
        query: str,
        method: Optional[SearchMethod] = None,
        top_k_semantic: int = 5,
        top_k_graph: int = 10,
        top_k_final: int = 10,
        org_id: Optional[str] = None
    ) -> HybridSearchResponse:
        """
        Perform hybrid search combining semantic and graph approaches.

        Args:
            query: Search query
            method: Search method to use (defaults to configured default)
            top_k_semantic: Number of semantic search results
            top_k_graph: Number of graph search results
            top_k_final: Number of final combined results
            org_id: Organization ID for filtering

        Returns:
            Complete hybrid search response
        """
        method = method or self.default_method
        logger.info(f"Starting hybrid search: '{query}' using method: {method.value}")

        # Initialize response structure
        response = HybridSearchResponse(
            query=query,
            method=method.value,
            answer="",
            semantic_search={"chunks": [], "total_chunks": 0},
            knowledge_graph={"relationships": [], "total_relationships": 0, "discovered_entities": []},
            source_files=[],
            confidence=0.0,
            processing_info={"pinecone_results": 0, "neo4j_results": 0, "combined_sources": 0}
        )

        try:
            if method == SearchMethod.SEMANTIC_ONLY:
                response = await self._semantic_only_search(query, top_k_semantic, org_id, response)
            elif method == SearchMethod.GRAPH_ONLY:
                response = await self._graph_only_search(query, top_k_graph, response)
            elif method == SearchMethod.TEMPLATE_HYBRID:
                response = await self._template_hybrid_search(query, top_k_semantic, top_k_graph, org_id, response)
            elif method == SearchMethod.DISCOVERY_HYBRID:
                response = await self._discovery_hybrid_search(query, top_k_semantic, top_k_graph, org_id, response)
            else:  # AUTO_HYBRID
                response = await self._auto_hybrid_search(query, top_k_semantic, top_k_graph, org_id, response)

            # Generate final answer
            response.answer = self._generate_answer(response)
            response.confidence = self._calculate_confidence(response)

            logger.info(f"Hybrid search completed with {len(response.semantic_search['chunks'])} semantic results and {len(response.knowledge_graph['relationships'])} graph relationships")

        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            response.answer = f"Search failed: {str(e)}"
            response.confidence = 0.0

        return response

    async def _semantic_only_search(
        self,
        query: str,
        top_k: int,
        org_id: Optional[str],
        response: HybridSearchResponse
    ) -> HybridSearchResponse:
        """Perform semantic-only search."""
        filters = {"org_id": org_id} if org_id else None
        semantic_results = await self.semantic_client.search(query, top_k, filters)

        response.semantic_search = {
            "chunks": [result["metadata"]["chunk_text"] for result in semantic_results],
            "total_chunks": len(semantic_results)
        }

        response.source_files = list(set(result["metadata"]["file_id"] for result in semantic_results))
        response.processing_info["pinecone_results"] = len(semantic_results)

        return response

    async def _graph_only_search(
        self,
        query: str,
        top_k: int,
        response: HybridSearchResponse
    ) -> HybridSearchResponse:
        """Perform graph-only search."""
        # Detect intent and build query
        intent = self.intent_detector.detect_intent(query)
        entities = self.intent_detector.extract_entities(query)

        cypher_query, parameters = self.query_builder.build_query(intent, entities)

        # Execute graph query
        graph_results = await self._execute_graph_query(cypher_query, parameters, top_k)

        response.knowledge_graph = {
            "relationships": graph_results,
            "total_relationships": len(graph_results),
            "discovered_entities": entities
        }

        response.processing_info["neo4j_results"] = len(graph_results)

        return response

    async def _template_hybrid_search(
        self,
        query: str,
        top_k_semantic: int,
        top_k_graph: int,
        org_id: Optional[str],
        response: HybridSearchResponse
    ) -> HybridSearchResponse:
        """Perform template-based hybrid search."""
        # Step 1: Semantic search
        filters = {"org_id": org_id} if org_id else None
        semantic_results = await self.semantic_client.search(query, top_k_semantic, filters)

        # Step 2: Graph search using templates
        intent = self.intent_detector.detect_intent(query)
        entities = self.intent_detector.extract_entities(query)

        cypher_query, parameters = self.query_builder.build_query(intent, entities)
        graph_results = await self._execute_graph_query(cypher_query, parameters, top_k_graph)

        # Step 3: Combine results
        response.semantic_search = {
            "chunks": [result["metadata"]["chunk_text"] for result in semantic_results],
            "total_chunks": len(semantic_results)
        }

        response.knowledge_graph = {
            "relationships": graph_results,
            "total_relationships": len(graph_results),
            "discovered_entities": entities
        }

        response.source_files = list(set(result["metadata"]["file_id"] for result in semantic_results))
        response.processing_info.update({
            "pinecone_results": len(semantic_results),
            "neo4j_results": len(graph_results),
            "combined_sources": len(response.source_files)
        })

        return response

    async def _discovery_hybrid_search(
        self,
        query: str,
        top_k_semantic: int,
        top_k_graph: int,
        org_id: Optional[str],
        response: HybridSearchResponse
    ) -> HybridSearchResponse:
        """Perform entity discovery-based hybrid search."""
        # Step 1: Semantic search first
        filters = {"org_id": org_id} if org_id else None
        semantic_results = await self.semantic_client.search(query, top_k_semantic, filters)

        # Step 2: Extract entities from semantic results
        discovered_entities = self._extract_entities_from_chunks(semantic_results)

        # Step 3: Query graph for relationships involving discovered entities
        graph_results = []
        for entity in discovered_entities:
            entity_query = """
                MATCH (e:Entity)-[r]->(t:Entity)
                WHERE e.name CONTAINS $entity_name
                RETURN e.name, e.type, type(r), t.name, t.type
                LIMIT $limit
            """
            entity_results = await self._execute_graph_query(
                entity_query,
                {"entity_name": entity, "limit": max(1, top_k_graph // len(discovered_entities))},
                top_k_graph
            )
            graph_results.extend(entity_results)

        # Step 4: Combine results
        response.semantic_search = {
            "chunks": [result["metadata"]["chunk_text"] for result in semantic_results],
            "total_chunks": len(semantic_results)
        }

        response.knowledge_graph = {
            "relationships": graph_results,
            "total_relationships": len(graph_results),
            "discovered_entities": discovered_entities
        }

        response.source_files = list(set(result["metadata"]["file_id"] for result in semantic_results))
        response.processing_info.update({
            "pinecone_results": len(semantic_results),
            "neo4j_results": len(graph_results),
            "combined_sources": len(response.source_files)
        })

        return response

    async def _auto_hybrid_search(
        self,
        query: str,
        top_k_semantic: int,
        top_k_graph: int,
        org_id: Optional[str],
        response: HybridSearchResponse
    ) -> HybridSearchResponse:
        """Automatically choose the best hybrid search approach."""
        # Detect intent to choose approach
        intent = self.intent_detector.detect_intent(query)

        if intent == QueryIntent.GENERAL_SEARCH:
            # Use discovery approach for general queries
            return await self._discovery_hybrid_search(query, top_k_semantic, top_k_graph, org_id, response)
        else:
            # Use template approach for specific intents
            return await self._template_hybrid_search(query, top_k_semantic, top_k_graph, org_id, response)

    async def _execute_graph_query(self, cypher_query: str, parameters: Dict[str, Any], limit: int) -> List[Dict[str, Any]]:
        """Execute a Cypher query and return formatted results."""
        try:
            # Debug logging
            logger.info(f"Executing query: {cypher_query}")
            logger.info(f"With parameters: {parameters}")

            # Use the driver directly to avoid Result consumption issues
            driver = self.neo4j_client._get_driver()

            with driver.session(database=self.neo4j_client.connection.database) as session:
                result = session.run(cypher_query, parameters)

                # Format results for consistency
                formatted_results = []
                count = 0
                for record in result:
                    if count >= limit:
                        break
                    # Convert Neo4j record to dictionary
                    result_dict = dict(record)
                    formatted_results.append(result_dict)
                    count += 1

                logger.info(f"Query returned {len(formatted_results)} results")
                return formatted_results

        except Exception as e:
            logger.error(f"Graph query execution failed: {e}")
            return []

    def _extract_entities_from_chunks(self, semantic_results: List[Dict[str, Any]]) -> List[str]:
        """Extract entity names from semantic search results."""
        entities = set()

        for result in semantic_results:
            chunk_text = result["metadata"]["chunk_text"]

            # Simple entity extraction from chunk text
            # Look for capitalized words that might be entity names
            words = chunk_text.split()
            for word in words:
                # Remove punctuation and check if it looks like an entity
                clean_word = re.sub(r'[^\w\s]', '', word)
                if (clean_word and
                    clean_word[0].isupper() and
                    len(clean_word) > 2 and
                    not clean_word.lower() in ['the', 'and', 'for', 'with', 'this', 'that']):
                    entities.add(clean_word)

        return list(entities)[:10]  # Limit to top 10 entities

    def _generate_answer(self, response: HybridSearchResponse) -> str:
        """Generate a comprehensive answer from search results."""
        semantic_chunks = response.semantic_search.get("chunks", [])
        relationships = response.knowledge_graph.get("relationships", [])

        if not semantic_chunks and not relationships:
            return "No relevant information found for your query."

        answer_parts = []

        # Add semantic context
        if semantic_chunks:
            answer_parts.append("Based on the available documents:")
            for i, chunk in enumerate(semantic_chunks[:3], 1):
                # Truncate long chunks
                truncated_chunk = chunk[:200] + "..." if len(chunk) > 200 else chunk
                answer_parts.append(f"{i}. {truncated_chunk}")

        # Add structured relationships
        if relationships:
            answer_parts.append("\nKnowledge graph relationships:")

            # Group relationships by type for better presentation
            relationship_groups = {}
            for rel in relationships[:10]:  # Limit to top 10
                rel_type = rel.get("type(r)", "RELATED_TO")
                if rel_type not in relationship_groups:
                    relationship_groups[rel_type] = []
                relationship_groups[rel_type].append(rel)

            for rel_type, rels in relationship_groups.items():
                answer_parts.append(f"\n{rel_type.replace('_', ' ').title()}:")
                for rel in rels[:3]:  # Limit to top 3 per type
                    source = rel.get("e.name", rel.get("person", rel.get("source.name", "Unknown")))
                    target = rel.get("t.name", rel.get("target", rel.get("system.name", "Unknown")))
                    answer_parts.append(f"  - {source} → {target}")

        return "\n".join(answer_parts)

    def _calculate_confidence(self, response: HybridSearchResponse) -> float:
        """Calculate confidence score for the search results."""
        semantic_count = response.semantic_search.get("total_chunks", 0)
        graph_count = response.knowledge_graph.get("total_relationships", 0)

        # Base confidence on result availability and diversity
        confidence = 0.0

        if semantic_count > 0:
            confidence += min(0.5, semantic_count * 0.1)  # Up to 0.5 for semantic results

        if graph_count > 0:
            confidence += min(0.4, graph_count * 0.05)  # Up to 0.4 for graph results

        # Bonus for having both types of results
        if semantic_count > 0 and graph_count > 0:
            confidence += 0.1

        return min(1.0, confidence)


def create_hybrid_search_engine(
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    pinecone_index_name: Optional[str] = None,
    pinecone_api_key: Optional[str] = None,
    default_method: str = "auto_hybrid"
) -> HybridSearchEngine:
    """
    Create a hybrid search engine with the specified configuration.

    Args:
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        pinecone_index_name: Pinecone index name (optional)
        pinecone_api_key: Pinecone API key (optional)
        default_method: Default search method

    Returns:
        Configured HybridSearchEngine instance
    """
    from storage.neo4j_client import Neo4jClient, Neo4jConnection

    # Create Neo4j client
    neo4j_conn = Neo4jConnection(
        uri=neo4j_uri,
        user=neo4j_user,
        password=neo4j_password
    )
    neo4j_client = Neo4jClient(neo4j_conn)

    # Create semantic search client
    semantic_client = SemanticSearchClient(
        index_name=pinecone_index_name,
        api_key=pinecone_api_key
    )

    # Convert method string to enum
    method_enum = SearchMethod(default_method)

    return HybridSearchEngine(
        neo4j_client=neo4j_client,
        semantic_client=semantic_client,
        default_method=method_enum
    )
