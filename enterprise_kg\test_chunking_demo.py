#!/usr/bin/env python3
"""
Test Script for Enterprise KG Minimal - Chunking Demo

This script tests the clean modular package with a sample document that requires chunking.
It will create a file node first and then process the document to demonstrate the 
File → Chunks → Chunk Graphs structure in Neo4j.

Prerequisites:
1. Neo4j running on bolt://localhost:7687
2. Valid Neo4j credentials
3. OpenAI or Anthropic API key set in environment

Usage:
    python test_chunking_demo.py
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_sample_document():
    """Create a sample document that will require chunking."""
    return """
# Project Atlas - AI-driven Customer Insights Platform

## Executive Summary
Project Atlas is TechCorp's flagship initiative to develop an AI-driven customer insights platform. 
The project is led by <PERSON>, Senior Project Manager, and sponsored by <PERSON>, VP of Engineering.
The initiative aims to revolutionize how we understand and serve our customers through advanced analytics.

## Team Structure
The core team consists of several key members:
- <PERSON> serves as the Project Manager and reports directly to <PERSON>
- Dr. <PERSON> leads the Data Science team and specializes in machine learning algorithms
- <PERSON> manages the Engineering team and oversees technical implementation
- <PERSON> handles Product Management and customer requirements gathering
- <PERSON> leads the DevOps team and manages cloud infrastructure

## Technical Architecture
The platform leverages cutting-edge technologies including:
- Python and TensorFlow for machine learning model development
- AWS cloud services for scalable infrastructure
- Apache Kafka for real-time data streaming
- PostgreSQL for structured data storage
- Redis for caching and session management
- Docker and Kubernetes for containerization and orchestration

## Project Timeline and Milestones
Phase 1 (Q1 2024): Research and prototyping
- Market analysis and competitive research
- Technology stack evaluation
- Initial prototype development
- Stakeholder feedback collection

Phase 2 (Q2 2024): Core development
- Machine learning model training
- API development and testing
- Database schema design
- Security framework implementation

Phase 3 (Q3 2024): Integration and testing
- System integration testing
- Performance optimization
- User acceptance testing
- Security audits and compliance

Phase 4 (Q4 2024): Deployment and launch
- Production deployment
- User training and documentation
- Go-to-market strategy execution
- Post-launch monitoring and support

## Business Impact
The Customer Insights Platform is expected to:
- Increase customer satisfaction by 25%
- Reduce customer churn by 15%
- Improve marketing campaign effectiveness by 40%
- Generate additional revenue of $2M annually

## Risk Management
Key risks and mitigation strategies:
- Technical complexity: Regular architecture reviews and proof-of-concepts
- Resource constraints: Cross-training and flexible team allocation
- Market changes: Agile development methodology and regular stakeholder feedback
- Compliance requirements: Early engagement with legal and compliance teams

## Stakeholder Engagement
Regular communication with key stakeholders:
- Weekly status reports to Sarah Johnson and executive team
- Bi-weekly demos to product stakeholders
- Monthly steering committee meetings
- Quarterly board presentations

The project has strong executive support and is considered critical to TechCorp's digital transformation strategy.
"""

def setup_neo4j_connection():
    """Setup Neo4j connection with environment variables."""
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    print(f"🔗 Connecting to Neo4j at {neo4j_uri}")
    print(f"   User: {neo4j_user}")
    print(f"   Password: {'*' * len(neo4j_password)}")
    
    return neo4j_uri, neo4j_user, neo4j_password

def setup_llm_config():
    """Setup LLM configuration."""
    # Check for available API keys in priority order
    requesty_key = os.getenv("REQUESTY_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")

    if requesty_key:
        print("🤖 Using Requesty with Claude 3.5 Sonnet model")
        return "requesty", "anthropic/claude-3-5-sonnet-20241022", requesty_key
    elif openai_key:
        print("🤖 Using OpenAI GPT-4o")
        return "openai", "gpt-4o", openai_key
    elif anthropic_key:
        print("🤖 Using Anthropic Claude")
        return "anthropic", "claude-3-5-sonnet-latest", anthropic_key
    else:
        print("❌ No LLM API key found. Please set REQUESTY_API_KEY, OPENAI_API_KEY or ANTHROPIC_API_KEY")
        return None, None, None

def create_file_node_manually(neo4j_uri, neo4j_user, neo4j_password, file_id):
    """Create the file node manually in Neo4j first."""
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        with driver.session() as session:
            # Create the file node
            query = """
            MERGE (f:File {id: $file_id})
            ON CREATE SET 
                f.name = $file_name,
                f.type = "Document",
                f.created_at = datetime(),
                f.source = "test_demo"
            ON MATCH SET f.last_updated = datetime()
            RETURN f
            """
            
            result = session.run(query, {
                "file_id": file_id,
                "file_name": "Project Atlas Document"
            })

            file_node = result.single()
            if file_node and file_node['f']:
                print(f"✅ File node created/updated: {file_id}")
                return True
            else:
                print(f"❌ Failed to create file node: {file_id}")
                return False
                
        driver.close()
        
    except Exception as e:
        print(f"❌ Error creating file node: {e}")
        return False

def test_chunking_demo():
    """Run the complete chunking demo."""
    print("🧪 Enterprise KG Minimal - Chunking Demo")
    print("=" * 60)
    
    # Setup configurations
    neo4j_uri, neo4j_user, neo4j_password = setup_neo4j_connection()
    llm_provider, llm_model, llm_api_key = setup_llm_config()
    
    if not llm_provider:
        return False
    
    # File ID for our test
    file_id = "project_atlas_doc_001"
    
    # Step 1: Create file node manually
    print(f"\n📁 Step 1: Creating file node '{file_id}' in Neo4j...")
    if not create_file_node_manually(neo4j_uri, neo4j_user, neo4j_password, file_id):
        return False
    
    # Step 2: Get sample document
    print("\n📄 Step 2: Preparing sample document...")
    sample_content = create_sample_document()
    print(f"   Document length: {len(sample_content)} characters")
    print(f"   Expected chunks: ~{len(sample_content) // 1000} chunks (with 1000 char chunks)")
    
    # Step 3: Process document with chunking
    print(f"\n🔄 Step 3: Processing document with chunking...")
    try:
        from enterprise_kg_minimal import process_document
        
        result = process_document(
            file_id=file_id,
            file_content=sample_content,
            neo4j_uri=neo4j_uri,
            neo4j_user=neo4j_user,
            neo4j_password=neo4j_password,
            llm_provider=llm_provider,
            llm_model=llm_model,
            llm_api_key=llm_api_key,
            chunking_strategy="hybrid",
            chunk_size=1000,
            chunk_overlap=200
        )
        
        # Step 4: Display results
        print("\n📊 Step 4: Processing Results")
        print("-" * 40)
        
        if result["success"]:
            print(f"✅ Processing successful!")
            print(f"   📄 File ID: {result['file_id']}")
            print(f"   🧩 Chunks created: {result['chunks_created']}")
            print(f"   ✅ Chunks processed: {result['chunks_processed']}")
            print(f"   ❌ Chunks failed: {result['chunks_failed']}")
            print(f"   👥 Total entities: {result['total_entities']}")
            print(f"   🔗 Total relationships: {result['total_relationships']}")
            print(f"   📁 File node created: {result['file_node_created']}")
            print(f"   🔗 CONTAINS relationships: {result['contains_relationships_created']}")
            
            # Show chunk details
            print(f"\n📋 Chunk Details:")
            for i, chunk_detail in enumerate(result['chunk_details']):
                status = "✅" if chunk_detail['graph_stored'] else "❌"
                print(f"   {status} Chunk {i}: {chunk_detail['entities_extracted']} entities, {chunk_detail['relationships_extracted']} relationships")
                if chunk_detail['error']:
                    print(f"      Error: {chunk_detail['error']}")
            
            # Step 5: Neo4j Browser queries
            print(f"\n🔍 Step 5: Neo4j Browser Queries")
            print("-" * 40)
            print("Open Neo4j Browser and run these queries to see the structure:")
            print()
            print("1. View the complete graph structure:")
            print(f"   MATCH (f:File {{id: '{file_id}'}})-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)")
            print("   RETURN f, c, e")
            print()
            print("2. View just the file and chunks:")
            print(f"   MATCH (f:File {{id: '{file_id}'}})-[:CONTAINS]->(c:Chunk)")
            print("   RETURN f, c")
            print()
            print("3. View entities and their relationships:")
            print("   MATCH (e1:Entity)-[r]->(e2:Entity)")
            print("   RETURN e1.name, type(r), e2.name")
            print()
            print("4. Count nodes by type:")
            print("   MATCH (n) RETURN labels(n), count(n)")
            
            return True
            
        else:
            print(f"❌ Processing failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chunking_demo()
    
    if success:
        print(f"\n🎉 Demo completed successfully!")
        print("   Open Neo4j Browser to explore the graph structure")
    else:
        print(f"\n💥 Demo failed. Check the error messages above.")
    
    sys.exit(0 if success else 1)
