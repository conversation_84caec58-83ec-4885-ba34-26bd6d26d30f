# Enterprise KG Minimal - Clean Modular Package

A clean, modular enterprise knowledge graph system that creates chunk-based knowledge graphs from documents.

## Features

- **Chunk-Based Architecture**: File → Chunks → Chunk Graphs structure
- **Importable Function**: Simple `process_document()` function interface
- **Document Chunking**: Advanced chunking strategies for optimal knowledge extraction
- **Entity Extraction**: LLM-powered entity and relationship extraction per chunk
- **Neo4j Storage**: Graph database storage with proper chunk relationships
- **Multiple LLM Providers**: Support for OpenAI, Anthropic, Gemini, OpenRouter, and Requesty
- **Modular Design**: Clean separation of concerns with core modules

## Architecture

### Module Structure

```
enterprise_kg_minimal/
├── __init__.py                    # Main importable function
├── core/                          # Core processing modules
│   ├── __init__.py
│   ├── document_processor.py      # Main processing logic
│   ├── chunking_engine.py         # Document chunking
│   └── graph_builder.py           # Graph construction
├── llm/                          # LLM integration
│   ├── __init__.py
│   └── client.py                 # LLM client
├── storage/                      # Data storage
│   ├── __init__.py
│   └── neo4j_client.py          # Neo4j operations
├── constants/                    # Constants and schemas
│   ├── __init__.py
│   ├── entities.py
│   ├── relationships.py
│   └── schemas.py
└── utils/                        # Utilities
    ├── __init__.py
    └── helpers.py
```

### Graph Structure

The system creates a hierarchical graph structure:

1. **File Node**: Represents the source document
2. **Chunk Nodes**: Individual chunks of the document
3. **Entity Nodes**: Extracted entities from each chunk
4. **Relationships**:
   - `CONTAINS`: File → Chunk
   - `EXTRACTED_FROM`: Chunk → Entity
   - Domain relationships between entities

## Quick Start

### Installation

```bash
# Install required packages
pip install neo4j openai anthropic python-dotenv
```

### Basic Usage

```python
from enterprise_kg_minimal import process_document

# Process a document
result = process_document(
    file_id="doc_123",
    file_content="Your document content here...",
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password"
)

# Check results
if result["success"]:
    print(f"Created {result['chunks_created']} chunks")
    print(f"Extracted {result['total_entities']} entities")
    print(f"Created {result['total_relationships']} relationships")
else:
    print(f"Error: {result['error']}")
```

### Advanced Configuration

```python
result = process_document(
    file_id="doc_123",
    file_content=content,
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="anthropic",
    llm_model="claude-3-5-sonnet-latest",
    chunking_strategy="hybrid",
    chunk_size=1500,
    chunk_overlap=300
)
```

## Chunking Strategies

The system supports multiple chunking strategies:

- **`fixed_size`**: Fixed character-based chunks
- **`sentence_based`**: Sentence boundary preservation
- **`paragraph_based`**: Paragraph boundary preservation
- **`semantic_based`**: Semantic coherence optimization
- **`hybrid`**: Combination approach (default)

## LLM Providers

Supported providers:
- **OpenAI**: `gpt-4o`, `gpt-3.5-turbo`
- **Anthropic**: `claude-3-5-sonnet-latest`, `claude-3-haiku`
- **Gemini**: `gemini-pro`
- **OpenRouter**: Multiple models via OpenRouter
- **Requesty**: Custom routing service

## Environment Variables

Set up your environment:

```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# LLM Configuration (choose one)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GEMINI_API_KEY=your_gemini_key
OPENROUTER_API_KEY=your_openrouter_key
REQUESTY_API_KEY=your_requesty_key
```

## Result Structure

The `process_document()` function returns:

```python
{
    "success": True,
    "file_id": "doc_123",
    "chunks_created": 5,
    "chunks_processed": 5,
    "chunks_failed": 0,
    "total_entities": 25,
    "total_relationships": 18,
    "file_node_created": True,
    "contains_relationships_created": 5,
    "chunk_details": [
        {
            "chunk_id": "doc_123_chunk_0_abc123",
            "chunk_index": 0,
            "entities_extracted": 6,
            "relationships_extracted": 4,
            "graph_stored": True,
            "error": None
        },
        # ... more chunks
    ]
}
```

## Graph Queries

After processing, you can query the graph:

```cypher
// Find all chunks for a file
MATCH (f:File {id: "doc_123"})-[:CONTAINS]->(c:Chunk)
RETURN c

// Find entities extracted from a specific chunk
MATCH (c:Chunk {id: "doc_123_chunk_0_abc123"})-[:EXTRACTED_FROM]->(e:Entity)
RETURN e

// Find relationships between entities in the same chunk
MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e1:Entity)
MATCH (c)-[:EXTRACTED_FROM]->(e2:Entity)
MATCH (e1)-[r]->(e2)
RETURN e1, r, e2
```

## Error Handling

The system provides detailed error information:

```python
result = process_document(file_id="doc_123", file_content=content)

if not result["success"]:
    print(f"Processing failed: {result['error']}")

# Check individual chunk failures
for chunk_detail in result["chunk_details"]:
    if chunk_detail["error"]:
        print(f"Chunk {chunk_detail['chunk_id']} failed: {chunk_detail['error']}")
```

## Dependencies

- `neo4j`: Graph database driver
- `openai`: OpenAI API client (optional)
- `anthropic`: Anthropic API client (optional)
- `python-dotenv`: Environment configuration

## License

This package is part of the Enterprise KG system.
