#!/usr/bin/env python3
"""
Debug Neo4j queries
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from storage.neo4j_client import Neo4j<PERSON>lient, Neo4jConnection

def main():
    print("🔍 Debugging Neo4j Queries")
    print("="*50)
    
    try:
        conn = Neo4jConnection(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'), 
            password=os.getenv('NEO4J_PASSWORD')
        )
        client = Neo4jClient(conn)
        
        driver = client._get_driver()
        with driver.session() as session:
            # Test query for Atlas
            print("\n1. Testing Atlas relationships:")
            query = """
            MATCH (a)-[r]->(b)
            WHERE b.name CONTAINS 'Atlas'
            RETURN a.name, type(r), b.name
            LIMIT 10
            """
            
            result = session.run(query)
            for record in result:
                print(f"  - {record['a.name']} --[{record['type(r)']}]--> {record['b.name']}")
            
            # Test reverse relationships
            print("\n2. Testing reverse Atlas relationships:")
            query2 = """
            MATCH (a)-[r]->(b)
            WHERE a.name CONTAINS 'Atlas'
            RETURN a.name, type(r), b.name
            LIMIT 10
            """
            
            result2 = session.run(query2)
            for record in result2:
                print(f"  - {record['a.name']} --[{record['type(r)']}]--> {record['b.name']}")
            
            # Test person relationships
            print("\n3. Testing person relationships:")
            query3 = """
            MATCH (person)-[r]->(target)
            WHERE person.entity_type = 'Person'
            RETURN person.name, type(r), target.name, target.entity_type
            LIMIT 10
            """
            
            result3 = session.run(query3)
            for record in result3:
                print(f"  - {record['person.name']} --[{record['type(r)']}]--> {record['target.name']} ({record['target.entity_type']})")
            
            # Test the specific query we're using
            print("\n4. Testing specific hybrid search query:")
            query4 = """
            MATCH (person)-[r:INVOLVED_IN|LEADS|OWNS]->(target)
            WHERE target.name CONTAINS 'Atlas'
            AND (person:Person OR person.entity_type = 'Person')
            AND (target:Project OR target.entity_type = 'Project')
            RETURN person.name as person, person.entity_type as person_type, 
                   type(r) as relationship, target.name as target, target.entity_type as target_type
            """
            
            result4 = session.run(query4)
            found_results = False
            for record in result4:
                found_results = True
                print(f"  - {record['person']} ({record['person_type']}) --[{record['relationship']}]--> {record['target']} ({record['target_type']})")
            
            if not found_results:
                print("  - No results found for this query")
        
        client.close()
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
