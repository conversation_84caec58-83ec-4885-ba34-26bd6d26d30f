#!/usr/bin/env python3
"""
Basic test script for Enterprise KG Minimal

This script tests the basic functionality of the clean modular package.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test that the main function can be imported."""
    try:
        # Import from current directory
        from core.document_processor import process_document
        print("✓ Successfully imported process_document")
        return True
    except ImportError as e:
        print(f"✗ Failed to import process_document: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality with sample content."""
    try:
        from core.document_processor import process_document
        
        # Sample content
        sample_content = """
        <PERSON> is the project manager for the AI Initiative at TechCorp. 
        He works closely with <PERSON>, who leads the engineering team.
        The project involves developing a new customer service platform using machine learning.
        """
        
        # Test with minimal configuration (will fail without proper Neo4j/LLM setup)
        print("Testing basic function call...")
        
        # This will likely fail due to missing credentials, but should not crash on import
        result = process_document(
            file_id="test_doc_001",
            file_content=sample_content,
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="test"
        )
        
        # If we get here, the function executed (though it may have failed)
        print(f"✓ Function executed, success: {result.get('success', False)}")
        if not result.get('success'):
            print(f"  Expected failure reason: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Function execution failed: {e}")
        return False

def test_module_structure():
    """Test that all modules can be imported."""
    modules_to_test = [
        "core.chunking_engine",
        "core.graph_builder",
        "core.prompt_generator",
        "llm.client",
        "storage.neo4j_client",
        "constants.entities",
        "constants.relationships",
        "constants.schemas"
    ]
    
    success_count = 0
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ Successfully imported {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"✗ Failed to import {module_name}: {e}")
    
    print(f"\nModule import results: {success_count}/{len(modules_to_test)} successful")
    return success_count == len(modules_to_test)

def main():
    """Run all tests."""
    print("🧪 Testing Enterprise KG Minimal - Clean Modular Package")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_import),
        ("Module Structure Test", test_module_structure),
        ("Basic Functionality Test", test_basic_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The module is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        print("💡 Note: Functionality tests may fail without proper Neo4j/LLM setup.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
