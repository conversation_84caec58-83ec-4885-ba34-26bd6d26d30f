#!/usr/bin/env python3
"""
Quick script to check what's in Neo4j after the chunking demo
"""

import os
from dotenv import load_dotenv

load_dotenv()

def check_neo4j_results():
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI')
        user = os.getenv('NEO4J_USER') 
        password = os.getenv('NEO4J_PASSWORD')
        
        print(f"🔗 Connecting to Neo4j at {uri}")
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            print("\n📊 Database Contents:")
            print("-" * 40)
            
            # Check file node
            result = session.run('MATCH (f:File {id: "project_atlas_doc_001"}) RETURN f')
            files = list(result)
            print(f"📁 File nodes: {len(files)}")
            if files:
                file_node = files[0]['f']
                print(f"   File ID: {file_node.get('id')}")
                print(f"   File Name: {file_node.get('name')}")
            
            # Check chunks
            result = session.run('MATCH (f:File {id: "project_atlas_doc_001"})-[:CONTAINS]->(c:Chunk) RETURN c ORDER BY c.chunk_index')
            chunks = list(result)
            print(f"🧩 Chunks: {len(chunks)}")
            for i, chunk_record in enumerate(chunks[:3]):  # Show first 3
                chunk = chunk_record['c']
                print(f"   Chunk {i}: {chunk.get('id')} (words: {chunk.get('word_count')}, sentences: {chunk.get('sentence_count')})")
            if len(chunks) > 3:
                print(f"   ... and {len(chunks) - 3} more chunks")
            
            # Check entities
            result = session.run('MATCH (e:Entity) RETURN count(e) as entity_count')
            entity_count = result.single()['entity_count']
            print(f"👥 Entities: {entity_count}")
            
            # Check relationships
            result = session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count')
            relationships = list(result)
            print(f"🔗 Relationships:")
            for rel in relationships:
                print(f"   {rel['rel_type']}: {rel['count']}")
            
            print(f"\n🔍 Neo4j Browser Queries to try:")
            print("1. View file and chunks structure:")
            print('   MATCH (f:File {id: "project_atlas_doc_001"})-[:CONTAINS]->(c:Chunk)')
            print('   RETURN f, c')
            print()
            print("2. View chunk details:")
            print('   MATCH (c:Chunk) WHERE c.id STARTS WITH "project_atlas_doc_001"')
            print('   RETURN c.id, c.chunk_index, c.word_count, c.sentence_count, substring(c.text, 0, 100) + "..." as preview')
            print('   ORDER BY c.chunk_index')
            print()
            print("3. Count all nodes by type:")
            print('   MATCH (n) RETURN labels(n), count(n)')
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    check_neo4j_results()
